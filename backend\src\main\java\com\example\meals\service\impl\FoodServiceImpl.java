package com.example.meals.service.impl;

import com.example.meals.common.Result;
import com.example.meals.dto.FoodSearchResponse;
import com.example.meals.dto.NutritionResponse;
import com.example.meals.entity.ChinaFood;
import com.example.meals.entity.ChinaFoodCategory;
import com.example.meals.mapper.ChinaFoodMapper;
import com.example.meals.mapper.ChinaFoodCategoryMapper;
import com.example.meals.service.FoodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 食物服务实现类
 */
@Service
public class FoodServiceImpl implements FoodService {

    @Autowired
    private ChinaFoodMapper chinaFoodMapper;

    @Autowired
    private ChinaFoodCategoryMapper categoryMapper;

    // 分类缓存
    private Map<String, String> categoryCache = new HashMap<>();

    @PostConstruct
    public void initCategoryCache() {
        try {
            List<ChinaFoodCategory> categories = categoryMapper.getAllCategories();
            for (ChinaFoodCategory category : categories) {
                categoryCache.put(category.getCategoryCode(), category.getCategoryName());
            }
        } catch (Exception e) {
            // 如果缓存初始化失败，使用默认映射
            initDefaultCategoryCache();
        }
    }

    private void initDefaultCategoryCache() {
        categoryCache.put("01", "谷类及其制品");
        categoryCache.put("02", "薯类、淀粉及其制品");
        categoryCache.put("03", "干豆类及其制品");
        categoryCache.put("04", "蔬菜类及其制品");
        categoryCache.put("05", "菌藻类");
        categoryCache.put("06", "水果类及其制品");
        categoryCache.put("07", "坚果、种子类");
        categoryCache.put("08", "畜肉类及其制品");
        categoryCache.put("09", "禽肉类及其制品");
        categoryCache.put("10", "乳类及其制品");
        categoryCache.put("11", "蛋类及其制品");
        categoryCache.put("12", "鱼虾蟹贝类");
        categoryCache.put("13", "调料类");
        categoryCache.put("14", "饮料类");
        categoryCache.put("15", "酒类");
        categoryCache.put("16", "其他");
        categoryCache.put("19", "油脂类");
        categoryCache.put("21", "特殊食品类");
    }

    /**
     * 从缓存中获取分类名称
     */
    private String getCategoryNameFromCache(String foodCode) {
        if (foodCode == null || foodCode.length() < 2) {
            return "未知分类";
        }
        String categoryCode = foodCode.substring(0, 2);
        return categoryCache.getOrDefault(categoryCode, "未知分类");
    }
    
    @Override
    public Result<List<FoodSearchResponse>> searchFoods(String keyword, Integer limit) {
        try {
            // 参数验证
            if (!StringUtils.hasText(keyword)) {
                return Result.badRequest("搜索关键词不能为空");
            }
            
            if (keyword.trim().length() < 2) {
                return Result.badRequest("搜索关键词至少需要2个字符");
            }
            
            // 设置默认限制
            if (limit == null || limit <= 0) {
                limit = 10;
            }
            if (limit > 50) {
                limit = 50; // 最大限制50条
            }
            
            // 搜索食物
            List<ChinaFood> foods = chinaFoodMapper.searchByName(keyword.trim(), limit);
            
            // 转换为响应DTO
            List<FoodSearchResponse> responses = foods.stream()
                    .map(food -> new FoodSearchResponse(food, getCategoryNameFromCache(food.getFoodCode())))
                    .collect(Collectors.toList());
            
            return Result.success(responses);
            
        } catch (Exception e) {
            return Result.error("搜索食物失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<List<String>> getAllCategories() {
        try {
            // 直接从分类表获取分类名称
            List<ChinaFoodCategory> categories = categoryMapper.getAllCategories();

            // 转换为分类名称列表
            List<String> categoryNames = categories.stream()
                    .map(ChinaFoodCategory::getCategoryName)
                    .collect(Collectors.toList());

            return Result.success(categoryNames);

        } catch (Exception e) {
            return Result.error("获取食物分类失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<List<FoodSearchResponse>> getFoodsByCategory(String categoryCode, Integer limit) {
        try {
            // 参数验证
            if (!StringUtils.hasText(categoryCode)) {
                return Result.badRequest("分类代码不能为空");
            }
            
            // 设置默认限制
            if (limit == null || limit <= 0) {
                limit = 20;
            }
            if (limit > 100) {
                limit = 100; // 最大限制100条
            }
            
            // 获取分类食物
            List<ChinaFood> foods = chinaFoodMapper.getFoodsByCategory(categoryCode, limit);
            
            // 转换为响应DTO
            List<FoodSearchResponse> responses = foods.stream()
                    .map(food -> new FoodSearchResponse(food, getCategoryNameFromCache(food.getFoodCode())))
                    .collect(Collectors.toList());
            
            return Result.success(responses);
            
        } catch (Exception e) {
            return Result.error("获取分类食物失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<List<FoodSearchResponse>> getPopularFoods(Integer limit) {
        try {
            // 设置默认限制
            if (limit == null || limit <= 0) {
                limit = 6;
            }
            if (limit > 20) {
                limit = 20; // 最大限制20条
            }
            
            // 获取热门食物
            List<ChinaFood> foods = chinaFoodMapper.getPopularFoods(limit);
            
            // 转换为响应DTO
            List<FoodSearchResponse> responses = foods.stream()
                    .map(food -> new FoodSearchResponse(food, getCategoryNameFromCache(food.getFoodCode())))
                    .collect(Collectors.toList());
            
            return Result.success(responses);
            
        } catch (Exception e) {
            return Result.error("获取热门食物失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<NutritionResponse> analyzeNutrition(Long foodId, Integer weight) {
        try {
            // 参数验证
            if (foodId == null || foodId <= 0) {
                return Result.badRequest("食物ID不能为空");
            }
            
            // 设置默认重量
            if (weight == null || weight <= 0) {
                weight = 100;
            }
            if (weight > 10000) {
                weight = 10000; // 最大限制10kg
            }
            
            // 获取食物详情
            ChinaFood food = chinaFoodMapper.getFoodById(foodId);
            if (food == null) {
                return Result.notFound("食物不存在");
            }
            
            // 创建营养分析响应
            NutritionResponse response = new NutritionResponse(food, weight);
            
            return Result.success(response);
            
        } catch (Exception e) {
            return Result.error("营养分析失败：" + e.getMessage());
        }
    }
    
    @Override
    public Result<FoodSearchResponse> getFoodById(Long foodId) {
        try {
            // 参数验证
            if (foodId == null || foodId <= 0) {
                return Result.badRequest("食物ID不能为空");
            }
            
            // 获取食物详情
            ChinaFood food = chinaFoodMapper.getFoodById(foodId);
            if (food == null) {
                return Result.notFound("食物不存在");
            }
            
            // 转换为响应DTO
            FoodSearchResponse response = new FoodSearchResponse(food, getCategoryNameFromCache(food.getFoodCode()));
            
            return Result.success(response);
            
        } catch (Exception e) {
            return Result.error("获取食物详情失败：" + e.getMessage());
        }
    }

}
